#!/usr/bin/env node

/**
 * 개발 워크플로우 관리 스크립트
 * JIT 모드와 사전 빌드 모드 간 전환을 쉽게 할 수 있도록 도와줍니다.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const MODES = {
  JIT: 'jit',
  PREBUILD: 'prebuild'
};

function showHelp() {
  console.log(`
개발 워크플로우 관리 도구

사용법:
  node scripts/dev-workflow.js <command> [options]

명령어:
  start [mode]     개발 서버 시작 (mode: jit|prebuild, 기본값: jit)
  build [mode]     패키지 빌드 (mode: jit|prebuild, 기본값: prebuild)
  switch <mode>    모드 전환 (mode: jit|prebuild)
  status           현재 모드 상태 확인

예시:
  node scripts/dev-workflow.js start jit        # JIT 모드로 개발 서버 시작
  node scripts/dev-workflow.js start prebuild   # 사전 빌드 모드로 개발 서버 시작
  node scripts/dev-workflow.js build prebuild   # 사전 빌드 모드로 패키지 빌드
  node scripts/dev-workflow.js switch jit       # JIT 모드로 전환
  node scripts/dev-workflow.js status           # 현재 상태 확인
  `);
}

function getCurrentMode() {
  try {
    const envFile = fs.readFileSync('.env.development', 'utf8');
    const match = envFile.match(/BUILD_MODE=(\w+)/);
    return match ? match[1] : 'jit';
  } catch (error) {
    return 'jit';
  }
}

function setMode(mode) {
  const envContent = `# 개발 환경 설정
NODE_ENV=development

# 빌드 모드 설정
# jit: Just-In-Time 컴파일 (기본값, 실시간 컴파일)
# prebuild: 사전 빌드된 파일 사용
BUILD_MODE=${mode}`;

  fs.writeFileSync('.env.development', envContent);
  console.log(`✅ 모드가 ${mode}로 설정되었습니다.`);
}

function runCommand(command, env = {}) {
  try {
    execSync(command, {
      stdio: 'inherit',
      env: { ...process.env, ...env }
    });
  } catch (error) {
    console.error(`❌ 명령어 실행 실패: ${command}`);
    process.exit(1);
  }
}

function startDev(mode = 'jit') {
  console.log(`🚀 ${mode.toUpperCase()} 모드로 개발 서버를 시작합니다...`);
  
  setMode(mode);
  
  if (mode === MODES.PREBUILD) {
    console.log('📦 먼저 패키지들을 빌드합니다...');
    runCommand('pnpm build:prebuild');
  }
  
  const devCommand = mode === MODES.JIT ? 'pnpm dev' : 'pnpm dev:prebuild';
  runCommand(devCommand);
}

function buildPackages(mode = 'prebuild') {
  console.log(`🔨 ${mode.toUpperCase()} 모드로 패키지를 빌드합니다...`);
  
  const buildCommand = mode === MODES.JIT ? 
    'NODE_ENV=development BUILD_MODE=jit turbo run build:jit' : 
    'pnpm build:prebuild';
    
  runCommand(buildCommand);
}

function showStatus() {
  const currentMode = getCurrentMode();
  console.log(`
📊 현재 개발 환경 상태:
  모드: ${currentMode.toUpperCase()}
  설명: ${currentMode === 'jit' ? 'Just-In-Time 컴파일 (실시간)' : '사전 빌드된 파일 사용'}
  
💡 모드 전환: node scripts/dev-workflow.js switch ${currentMode === 'jit' ? 'prebuild' : 'jit'}
  `);
}

// 메인 실행 로직
const [,, command, option] = process.argv;

switch (command) {
  case 'start':
    startDev(option);
    break;
  case 'build':
    buildPackages(option);
    break;
  case 'switch':
    if (!option || !Object.values(MODES).includes(option)) {
      console.error('❌ 유효한 모드를 지정해주세요: jit 또는 prebuild');
      process.exit(1);
    }
    setMode(option);
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    console.error('❌ 알 수 없는 명령어입니다.');
    showHelp();
    process.exit(1);
}
