import { defineConfig } from "tsup";

const isDev = process.env.NODE_ENV === "development";
const isJIT =
  process.env.BUILD_MODE === "jit" || (!process.env.BUILD_MODE && isDev);

export default defineConfig({
  entry: ["src/index.ts"],
  format: ["esm", "cjs"],
  dts: true,
  target: "es2020",
  sourcemap: true,
  clean: true,
  // JIT 모드에서는 최적화를 최소화하여 빠른 빌드
  minify: !isJIT,
  splitting: !isJIT,
  treeshake: !isJIT,
  // JIT 모드에서는 더 자세한 소스맵 생성
  sourcemap: isJIT ? "inline" : true,
});
