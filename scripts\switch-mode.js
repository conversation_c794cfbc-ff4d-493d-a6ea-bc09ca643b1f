#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const PACKAGES = [
  'packages/geon-map/core',
  'packages/geon-map/react/odf'
];

function switchToMode(mode) {
  if (mode !== 'jit' && mode !== 'compiled') {
    console.error('❌ 모드는 "jit" 또는 "compiled"여야 합니다.');
    process.exit(1);
  }

  console.log(`🔄 ${mode.toUpperCase()} 모드로 전환 중...`);

  PACKAGES.forEach(packagePath => {
    const sourceFile = path.join(packagePath, `package.${mode}.json`);
    const targetFile = path.join(packagePath, 'package.json');

    if (!fs.existsSync(sourceFile)) {
      console.error(`❌ ${sourceFile} 파일을 찾을 수 없습니다.`);
      return;
    }

    try {
      fs.copyFileSync(sourceFile, targetFile);
      console.log(`✅ ${packagePath}/package.json → ${mode} 모드`);
    } catch (error) {
      console.error(`❌ ${packagePath} 전환 실패:`, error.message);
    }
  });

  console.log(`\n🎉 ${mode.toUpperCase()} 모드로 전환 완료!`);
  
  if (mode === 'jit') {
    console.log('\n💡 JIT 모드: 앱 번들러가 소스 파일을 직접 컴파일합니다.');
    console.log('   실행: pnpm dev');
  } else {
    console.log('\n💡 컴파일 모드: 미리 빌드된 파일을 사용합니다.');
    console.log('   실행: pnpm dev:compile');
  }
}

const mode = process.argv[2];
if (!mode) {
  console.log(`
사용법: node scripts/switch-mode.js <mode>

모드:
  jit      - Just-In-Time 모드 (소스 파일 직접 참조)
  compiled - 컴파일 모드 (빌드된 파일 사용)

예시:
  node scripts/switch-mode.js jit
  node scripts/switch-mode.js compiled
  `);
  process.exit(1);
}

switchToMode(mode);
