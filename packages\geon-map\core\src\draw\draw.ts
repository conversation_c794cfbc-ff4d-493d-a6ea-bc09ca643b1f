import { Feature } from "../feature/feature";
import { ODF } from "../odf";
import type {
  DrawControlOptions,
  DrawEventType,
  DrawingFeature,
  DrawingMode,
  DrawToolType,
  IDrawManager,
  MeasureControlOptions,
  MeasureMode,
} from "../types/draw";
import { Measure } from "./measure";

/**
 * Draw 클래스
 * 그리기 도구 관리, 이벤트 처리, 스타일 제어를 담당
 *
 * 책임 분리:
 * - Draw: ODF 인스턴스 관리, 이벤트 처리 로직
 * - Feature: Feature 관련 유틸리티 (좌표 추출, 타입 변환 등)
 * - React Store: UI 상태 관리 (selectedFeature, contextMenuInfo 등)
 */
export class Draw implements IDrawManager {
  private drawControl: any = null;
  private clearControl: any = null;
  private measureManager: Measure | null = null;
  private map: any = null;
  private eventListeners: Map<string, string> = new Map();
  private currentStyleOptions: any = null; // 현재 스타일 옵션 저장

  /**
   * DrawControl 인스턴스 초기화
   * measureOptions가 제공되면 MeasureControl도 함께 초기화
   */
  initialize(
    map: any,
    options: DrawControlOptions = {},
    measureOptions?: MeasureControlOptions,
  ): void {
    if (!map) {
      throw new Error("Map instance is required");
    }

    if (!ODF.isAvailable()) {
      throw new Error("ODF library is not available");
    }

    this.map = map;

    // 기본 옵션 설정
    const defaultOptions: DrawControlOptions = {
      continuity: false,
      measure: false,
      createNewLayer: false,
      // editFeatureMenu: ["modify", "dragTranslate", "delete", "setText"],
      tools: [
        "text",
        "polygon",
        "lineString",
        "box",
        "point",
        "circle",
        "curve",
        "buffer",
      ],
      style: {
        fill: {
          color: [254, 243, 255, 0.6],
        },
        stroke: {
          color: [103, 87, 197, 0.7],
          width: 2,
        },
        image: {
          circle: {
            fill: {
              color: [254, 243, 255, 0.6],
            },
            stroke: {
              color: [103, 87, 197, 0.7],
              width: 2,
            },
            radius: 5,
          },
        },
        text: {
          textAlign: "left",
          font: "30px sans-serif",
          fill: {
            color: [103, 87, 197, 1],
          },
          stroke: {
            color: [255, 255, 255, 1],
          },
        },
      },
      bufferStyle: {
        stroke: {
          color: [255, 255, 159, 1],
          width: 2,
        },
        fill: {
          color: [255, 255, 159, 0.2],
        },
      },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    // 현재 스타일 옵션 저장
    this.currentStyleOptions = mergedOptions.style || defaultOptions.style;

    // ODF DrawControl 인스턴스 생성
    this.drawControl = new ODF.DrawControl(mergedOptions);
    this.clearControl = new ODF.ClearControl();
    // 지도에 연결
    this.drawControl.setMap(map);
    this.clearControl.setMap(map);

    // MeasureControl 초기화 (옵션이 제공된 경우)
    if (measureOptions) {
      this.measureManager = new Measure();
      this.measureManager.initialize(map, measureOptions);
    }
  }

  /**
   * 피처에서 좌표 추출 (Feature 위임)
   */
  extractCoordinates(feature: any): any {
    return Feature.extractCoordinates(feature);
  }

  /**
   * DrawingMode를 ODF DrawToolType으로 변환
   */
  private mapDrawingModeToToolType(mode: DrawingMode): DrawToolType | null {
    if (!mode) return null;

    const modeMap: Record<string, DrawToolType> = {
      point: "point",
      lineString: "lineString",
      polygon: "polygon",
      rectangle: "box",
      circle: "circle",
      text: "text",
      curve: "curve",
      buffer: "buffer",
    };

    return modeMap[mode] || null;
  }

  /**
   * DrawingMode가 측정 모드인지 확인
   */
  private isMeasureMode(mode: DrawingMode): mode is MeasureMode {
    return mode !== null && mode.startsWith("measure-");
  }

  /**
   * 그리기 시작 (그리기 모드와 측정 모드 모두 지원)
   */
  startDrawing(mode: DrawingMode): void {
    if (!mode) {
      throw new Error("Invalid mode");
    }

    // 측정 모드인지 확인
    if (this.isMeasureMode(mode)) {
      if (!this.measureManager) {
        throw new Error(
          "MeasureControl is not initialized. Please provide measureOptions during initialization.",
        );
      }
      this.measureManager.startMeasuring(mode);
      return;
    }

    // 일반 그리기 모드 처리
    if (!this.drawControl) {
      throw new Error("DrawControl is not initialized");
    }

    const toolType = this.mapDrawingModeToToolType(mode);
    if (!toolType) {
      throw new Error(`Unsupported drawing mode: ${mode}`);
    }

    // 상태는 React 패키지에서 관리

    // ODF DrawControl의 해당 메서드 호출
    switch (toolType) {
      case "text":
        this.drawControl.drawText();
        break;
      case "polygon":
        this.drawControl.drawPolygon();
        break;
      case "lineString":
        this.drawControl.drawLineString();
        break;
      case "point":
        this.drawControl.drawPoint();
        break;
      case "circle":
        this.drawControl.drawCircle();
        break;
      case "curve":
        this.drawControl.drawCurve();
        break;
      case "box":
        this.drawControl.drawBox();
        break;
      case "buffer":
        this.drawControl.buffer();
        break;
      default:
        throw new Error(`Unsupported tool type: ${toolType}`);
    }
  }

  /**
   * 그리기 중지 (그리기 모드와 측정 모드 모두 지원)
   */
  stopDrawing(): void {
    // 측정 중지
    if (this.measureManager) {
      this.measureManager.stopMeasuring();
    }

    // 그리기 중지
    if (this.drawControl) {
      // 상태는 React 패키지에서 관리
      // ODF DrawControl의 clear 메서드로 현재 그리기 중단
      this.drawControl.clear();
    }
  }

  /**
   * 현재 그리기/측정 취소 (진행 중인 작업만 취소)
   */
  cancelCurrentDrawing(): void {
    // 측정 취소
    if (this.measureManager) {
      this.measureManager.stopMeasuring();
    }

    // 그리기 취소
    if (this.drawControl) {
      // ODF DrawControl의 현재 그리기 작업만 취소
      this.drawControl.clear();
    }
  }

  /**
   * 모든 그리기 및 측정 삭제 (Stateless)
   */
  clear(): void {
    console.log("Clearing all drawings and measurements");

    // 측정 결과 클리어
    if (this.measureManager) {
      this.measureManager.clear();
    }

    // 그리기 결과 클리어
    if (this.drawControl) {
      console.log("this drawControl", this.drawControl);

      // DrawControl 클리어
      this.drawControl.clear();

      // ClearControl도 실행 (지도의 모든 그리기 이벤트 삭제)
      if (this.clearControl) {
        try {
          this.clearControl.clear();
          console.log("ClearControl.clear() executed");
        } catch (error) {
          console.warn("ClearControl.clear() failed:", error);
        }
      } else {
        console.warn("ClearControl not available");
      }
    }

    // 상태는 React 패키지에서 관리
  }

  /**
   * 그리기 레이어 조회
   */
  getDrawLayer(): any {
    if (!this.drawControl) return null;
    return this.drawControl.findDrawVectorLayer();
  }

  /**
   * 스타일 업데이트 (Stateless)
   * 기존 그려진 도형들과 현재 그리기 중인 도형에 새로운 스타일을 적용합니다.
   */
  updateStyle(styleOptions: any): void {
    if (!this.drawControl) {
      console.warn("DrawControl is not initialized");
      return;
    }

    try {
      // 현재 스타일 옵션 업데이트
      this.currentStyleOptions = styleOptions;

      // ODF DrawControl의 setStyle 메서드를 사용하여 스타일 업데이트
      this.drawControl.setStyle(styleOptions);

      // 현재 그려진 모든 feature들에 새로운 스타일 적용
      const drawLayer = this.getDrawLayer();
      if (drawLayer) {
        const features = drawLayer.getFeatures();
        features.forEach((feature: any) => {
          this.applyStyleToFeature(feature, styleOptions);
        });
      }

      console.log("DrawManager style updated successfully");
    } catch (error) {
      console.error("Failed to update DrawManager style:", error);
    }
  }

  /**
   * 개별 feature에 스타일 적용
   */
  private applyStyleToFeature(feature: any, styleOptions: any): void {
    try {
      const geometryType = feature.getGeometry().getType().toLowerCase();
      const style = this.getStyleForGeometryType(geometryType, styleOptions);
      if (style) {
        feature.setStyle(style);
      }
    } catch (error) {
      console.error("Failed to apply style to feature:", error);
    }
  }

  /**
   * geometry 타입에 맞는 스타일 생성
   */
  private getStyleForGeometryType(
    geometryType: string,
    styleOptions: any,
  ): any {
    if (!ODF.isAvailable() || !styleOptions) return null;

    try {
      const styleFactory = (globalThis as any).odf.StyleFactory;

      // geometry 타입별 스타일 매핑
      const styleMapping: { [key: string]: any } = {
        linestring: { stroke: styleOptions.stroke },
        polygon: { stroke: styleOptions.stroke, fill: styleOptions.fill },
        circle: { stroke: styleOptions.stroke, fill: styleOptions.fill },
        point: { image: styleOptions.image || styleOptions.icon },
      };

      const mappedStyle = styleMapping[geometryType] || styleOptions;
      return styleFactory.produce({
        ...mappedStyle,
        text: styleOptions.text,
      });
    } catch (error) {
      console.error(
        `Failed to create style for geometry type ${geometryType}:`,
        error,
      );
      return null;
    }
  }

  /**
   * 현재 그리기 데이터를 GeoJSON 형태로 내보내기
   */
  exportToGeoJSON(): any {
    const drawLayer = this.getDrawLayer();
    if (!drawLayer) {
      console.warn("Draw layer not found");
      return null;
    }

    try {
      // ODF Layer의 toGeoJson 메서드 사용
      const geoJsonData = drawLayer.toGeoJson();
      return geoJsonData;
    } catch (error) {
      console.error("Failed to export to GeoJSON:", error);
      return null;
    }
  }

  /**
   * GeoJSON 데이터를 그리기 레이어에 불러오기 (Stateless)
   * @param geoJsonData GeoJSON 데이터
   * @param styleOptions 적용할 스타일 옵션 (선택사항)
   * @returns 생성된 DrawingFeature 배열
   */
  importFromGeoJSON(geoJsonData: any, styleOptions?: any): DrawingFeature[] {
    if (!this.drawControl || !geoJsonData) {
      console.warn("DrawControl not initialized or invalid GeoJSON data");
      return [];
    }

    try {
      const drawLayer = this.getDrawLayer();
      if (!drawLayer) {
        console.warn("Draw layer not found");
        return [];
      }

      // ODF LayerFactory를 사용하여 GeoJSON 레이어 생성
      const geoJsonLayer = (globalThis as any).odf.LayerFactory.produce(
        "geojson",
        {
          data: geoJsonData,
          dataProjectionCode: this.map.getView().getProjection().getCode(),
          featureProjectionCode: this.map.getView().getProjection().getCode(),
        },
      );

      // 임시로 지도에 추가하여 features 추출
      geoJsonLayer.setOpacity(0);
      geoJsonLayer.setMap(this.map);

      // features를 draw layer로 이동
      const features = geoJsonLayer.getFeatures();
      const importedFeatures: DrawingFeature[] = [];

      // 스타일 옵션 결정 (매개변수 > 현재 스타일 > 기본 스타일)
      const appliedStyle = styleOptions || this.getCurrentStyle();

      features.forEach((feature: any) => {
        const geometryType = feature.getGeometry().getType().toLowerCase();
        const style = this.getStyleForGeometryType(geometryType, appliedStyle);

        if (style) {
          feature.setStyle(style);
        }

        drawLayer.addFeature(feature);

        // DrawingFeature 객체 생성 (Feature 사용)
        const drawingFeature: DrawingFeature =
          Feature.convertToDrawingFeature(feature);

        importedFeatures.push(drawingFeature);
      });

      // 임시 레이어 제거
      geoJsonLayer.removeMap(this.map);

      console.log(
        `Successfully imported ${features.length} features from GeoJSON`,
      );

      return importedFeatures;
    } catch (error) {
      console.error("Failed to import from GeoJSON:", error);
      return [];
    }
  }

  /**
   * 현재 스타일 옵션 반환 (Stateless)
   */
  private getCurrentStyle(): any {
    return this.currentStyleOptions || this.getDefaultStyle();
  }

  /**
   * 기본 스타일 옵션 반환
   */
  private getDefaultStyle(): any {
    return {
      fill: {
        color: [254, 243, 255, 0.6],
      },
      stroke: {
        color: [103, 87, 197, 0.7],
        width: 2,
      },
      image: {
        circle: {
          fill: {
            color: [254, 243, 255, 0.6],
          },
          stroke: {
            color: [103, 87, 197, 0.7],
            width: 2,
          },
          radius: 5,
        },
      },
      text: {
        textAlign: "left",
        font: "30px sans-serif",
        fill: {
          color: [103, 87, 197, 1],
        },
        stroke: {
          color: [255, 255, 255, 1],
        },
      },
    };
  }

  // mapGeometryTypeToDrawingMode 제거 - FeatureManager 직접 사용

  /**
   * 이벤트 리스너 등록 (그리기 및 측정 모두 지원)
   */
  addEventListener(
    eventType: DrawEventType,
    handler: (feature: any) => void,
  ): string {
    if (!this.drawControl) {
      throw new Error("DrawControl is not initialized");
    }

    const eventId = (globalThis as any).odf.event.addListener(
      this.drawControl,
      eventType,
      handler,
      false, // expiration: false (영구 리스너)
    );

    const listenerId = `${eventType}_${Date.now()}`;
    this.eventListeners.set(listenerId, eventId);

    return listenerId;
  }

  /**
   * 측정 이벤트 리스너 등록
   */
  addMeasureEventListener(
    eventType: DrawEventType,
    handler: (feature: any) => void,
  ): string {
    if (!this.measureManager) {
      throw new Error("MeasureManager is not initialized");
    }

    return this.measureManager.addEventListener(eventType as any, handler);
  }

  /**
   * 이벤트 리스너 ID를 저장 (외부에서 접근 가능)
   */
  addEventListenerId(key: string, eventId: string): void {
    this.eventListeners.set(key, eventId);
  }

  /**
   * 지도 인스턴스 반환 (외부에서 접근 가능)
   */
  getMap(): any {
    return this.map;
  }

  /**
   * 이벤트 리스너 제거 (그리기 및 측정 모두 지원)
   */
  removeEventListener(listenerId: string): void {
    const eventId = this.eventListeners.get(listenerId);
    if (eventId) {
      // measureManager의 리스너인지 확인
      if (listenerId.startsWith("measure_") && this.measureManager) {
        this.measureManager.removeEventListener(eventId);
      } else {
        // 일반 drawControl 리스너
        (globalThis as any).odf.event.removeListener(eventId);
      }
      this.eventListeners.delete(listenerId);
    }
  }

  /**
   * DrawControl 인스턴스 반환
   */
  getDrawControl(): any {
    return this.drawControl;
  }

  // selectFeature 메서드 제거 - React Store에서 처리

  /**
   * feature 삭제 (Feature 위임)
   */
  deleteFeature(feature: any): boolean {
    if (!feature || !this.drawControl) {
      return false;
    }

    const drawLayer = this.getDrawLayer();
    return Feature.deleteFeature(drawLayer, feature);
  }

  /**
   * feature 스타일 변경 (Feature 위임)
   */
  changeFeatureStyle(feature: any, styleOptions: any): boolean {
    return Feature.changeFeatureStyle(feature, styleOptions);
  }

  /**
   * 우클릭 이벤트 리스너 등록
   *
   * ⚠️ 개선된 방식: contextMenuInfo 생성을 콜백에서 처리
   * DrawManager는 이벤트 감지만 담당, 상태 관리는 React Store에서
   */
  setupContextMenuListener(
    callback: (
      feature: any,
      coordinates: { x: number; y: number },
      position: [number, number],
    ) => void,
  ): void {
    if (!this.map) return;

    // ODF 이벤트 API 사용 (addListener 방식)
    const eventId = (globalThis as any).odf.event.addListener(
      this.map,
      "contextmenu",
      (evt: any) => {
        // 클릭한 위치의 feature 조회
        const features = evt.targetMap.selectFeatureOnClick({
          pixel: evt.pixel,
        });
        const drawLayerId = this.getDrawLayer()?.getODFId();

        const filteredFeatures = features.filter(
          (obj: any) => obj.layer.getODFId() === drawLayerId,
        );

        if (filteredFeatures.length === 0) {
          // feature가 없으면 null 전달
          callback(null, { x: 0, y: 0 }, [0, 0]);
          return;
        }

        const feature = filteredFeatures[0].feature;
        const coordinates = { x: evt.pixel[0], y: evt.pixel[1] };

        // pixel 좌표를 지리적 좌표로 변환
        const geographicCoords = this.map.getCoordinateFromPixel([
          coordinates.x,
          coordinates.y,
        ]) || [0, 0];

        // React Store에서 상태 관리하도록 콜백 호출
        callback(feature, coordinates, geographicCoords);
      },
      false, // expiration: false (영구 리스너)
    );

    this.eventListeners.set("contextmenu", eventId);
  }

  /**
   * 통합된 우클릭 이벤트 리스너 등록 (그리기 및 측정 레이어 모두 지원)
   *
   * drawLayer와 measureLayer 모두에서 feature를 조회하고,
   * layer type을 적절히 구분하여 설정합니다.
   */
  setupUnifiedContextMenuListener(
    callback: (
      feature: any,
      coordinates: { x: number; y: number },
      position: [number, number],
      layerType: "draw" | "measure" | null,
    ) => void,
  ): void {
    if (!this.map) return;

    // ODF 이벤트 API 사용 (addListener 방식)
    const eventId = (globalThis as any).odf.event.addListener(
      this.map,
      "contextmenu",
      (evt: any) => {
        // 클릭한 위치의 feature 조회
        const features = evt.targetMap.selectFeatureOnClick({
          pixel: evt.pixel,
        });

        const drawLayerId = this.getDrawLayer()?.getODFId();
        const measureLayerId = this.getMeasureLayer()?.getODFId();

        // drawLayer에서 feature 조회
        const drawFeatures = features.filter(
          (obj: any) => obj.layer.getODFId() === drawLayerId,
        );

        // measureLayer에서 feature 조회
        const measureFeatures = features.filter(
          (obj: any) => obj.layer.getODFId() === measureLayerId,
        );

        let selectedFeature = null;
        let layerType: "draw" | "measure" | null = null;

        // 우선순위: draw feature > measure feature
        if (drawFeatures.length > 0) {
          selectedFeature = drawFeatures[0].feature;
          layerType = "draw";
        } else if (measureFeatures.length > 0) {
          selectedFeature = measureFeatures[0].feature;
          layerType = "measure";
        }

        const coordinates = { x: evt.pixel[0], y: evt.pixel[1] };

        // pixel 좌표를 지리적 좌표로 변환
        const geographicCoords = this.map.getCoordinateFromPixel([
          coordinates.x,
          coordinates.y,
        ]) || [0, 0];

        // React Store에서 상태 관리하도록 콜백 호출
        callback(selectedFeature, coordinates, geographicCoords, layerType);
      },
      false, // expiration: false (영구 리스너)
    );

    this.eventListeners.set("unified_contextmenu", eventId);
  }

  /**
   * MeasureManager 인스턴스 반환
   */
  getMeasureManager(): Measure | null {
    return this.measureManager;
  }

  /**
   * MeasureControl 인스턴스 반환
   */
  getMeasureControl(): any {
    return this.measureManager?.getMeasureControl() || null;
  }

  /**
   * 측정 레이어 조회
   */
  getMeasureLayer(): any {
    return this.measureManager?.getMeasureLayer() || null;
  }

  /**
   * 지도에서 제거 (Stateless)
   */
  destroy(): void {
    // MeasureManager 제거
    if (this.measureManager) {
      this.measureManager.destroy();
      this.measureManager = null;
    }

    if (this.drawControl) {
      // 모든 이벤트 리스너 제거 (ODF 이벤트 API 사용)
      this.eventListeners.forEach((eventId) => {
        (globalThis as any).odf.event.removeListener(eventId);
      });
      this.eventListeners.clear();

      // DrawControl 제거
      this.drawControl.removeMap();
      this.drawControl = null;
    }

    if (this.clearControl) {
      this.clearControl.removeMap();
      this.clearControl = null;
    }

    this.map = null;
    this.currentStyleOptions = null;
    // 상태는 React 패키지에서 관리
  }
}
