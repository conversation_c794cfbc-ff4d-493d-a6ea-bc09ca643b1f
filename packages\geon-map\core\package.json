{"name": "@geon-map/core", "version": "0.0.1", "type": "module", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "default": "./src/index.ts"}}, "sideEffects": false, "files": ["src/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "prepublishOnly": "pnpm run build", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@geon-query/react-query": "workspace:*", "@types/node": "^22.17.0", "eslint": "^9.32.0", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^2.1.9"}, "publishConfig": {"access": "restricted"}}