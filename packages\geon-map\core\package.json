{"name": "@geon-map/core", "version": "0.0.1", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}}, "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "NODE_ENV=production BUILD_MODE=prebuild tsup", "build:jit": "NODE_ENV=development BUILD_MODE=jit tsup", "dev": "NODE_ENV=development BUILD_MODE=jit tsup --watch", "dev:prebuild": "NODE_ENV=development BUILD_MODE=prebuild tsup --watch", "prepublishOnly": "pnpm run build", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@geon-query/react-query": "workspace:*", "@types/node": "^22.17.0", "eslint": "^9.32.0", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^2.1.9"}, "publishConfig": {"access": "restricted"}}