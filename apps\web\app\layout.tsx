import "@config/tailwind/globals.css";

import { QueryProvider } from "@geon-query/react-query";
import {
  SidebarProvider,
  SidebarTrigger,
} from "@geon-ui/react/primitives/sidebar";
import type { Metadata } from "next";
import Script from "next/script";

import { geistMono, geistSans } from "@/assets/fonts";
import { AppSidebar } from "@/components/app-sidebar";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        {/* TODO provider는 하나로 합치기 */}
        <SidebarProvider>
          <AppSidebar />
          <main className="size-full">
            <SidebarTrigger />
            <QueryProvider>{children}</QueryProvider>
          </main>
        </SidebarProvider>
        <Script src="/js/odf.min.js" />
      </body>
    </html>
  );
}
