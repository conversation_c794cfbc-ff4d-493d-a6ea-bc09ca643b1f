{"name": "my-turbo", "private": true, "type": "module", "scripts": {"build": "turbo run build", "dev": "node scripts/switch-mode.js jit && pnpm --filter web dev", "dev:compile": "node scripts/switch-mode.js compiled && turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "check-types": "turbo run check-types", "prepare": "husky", "web": "pnpm --filter web", "storybook": "pnpm --filter web storybook", "release": "turbo run build --filter=./packages/**/* && changeset publish", "clean": "rimraf node_modules/.cache .turbo && rimraf packages/**/dist && rimraf apps/**/.next", "switch:jit": "node scripts/switch-mode.js jit", "switch:compiled": "node scripts/switch-mode.js compiled"}, "devDependencies": {"@changesets/cli": "^2.29.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "turbo": "^2.5.5", "typescript": "5.8.3"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "engines": {"node": ">=18"}, "dependencies": {"changesets-gitlab": "^0.13.3"}}