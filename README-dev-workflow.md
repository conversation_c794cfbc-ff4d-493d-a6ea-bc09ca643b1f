# 개발 워크플로우

이 프로젝트는 Node.js의 conditional exports를 사용하여 개발/프로덕션 모드를 자동으로 전환합니다.

## 🚀 사용법

### JIT 모드 (개발)
```bash
# 개발 모드: 소스 파일 직접 참조, web 앱만 실행
pnpm dev
```
- `NODE_ENV=development`로 실행
- 패키지의 `exports.development` 조건 사용
- 소스 파일(`src/index.ts`)을 직접 참조
- Next.js가 `transpilePackages`로 실시간 컴파일

### 컴파일 모드 (프로덕션 유사)
```bash
# 컴파일 모드: 빌드된 파일 사용, 모든 패키지 실행
pnpm dev:compile
```
- `NODE_ENV=production`으로 실행
- 패키지의 `exports.production` 조건 사용
- 빌드된 파일(`dist/index.js`)을 참조
- 패키지들이 `tsup --watch`로 실시간 빌드

## 📁 패키지 설정

```json
{
  "exports": {
    ".": {
      "development": {
        "types": "./src/index.ts",
        "import": "./src/index.ts",
        "default": "./src/index.ts"
      },
      "production": {
        "types": "./dist/index.d.ts",
        "import": "./dist/index.js",
        "require": "./dist/index.cjs",
        "default": "./dist/index.js"
      },
      "default": {
        "types": "./dist/index.d.ts",
        "import": "./dist/index.js",
        "require": "./dist/index.cjs",
        "default": "./dist/index.js"
      }
    }
  }
}
```

## 💡 언제 어떤 모드를 사용할까?

- **일반 개발**: `pnpm dev` (빠른 핫 리로드)
- **성능 테스트**: `pnpm dev:compile` (프로덕션 유사 환경)
- **번들 크기 확인**: `pnpm dev:compile`
- **배포 전 검증**: `pnpm dev:compile`

이 방식은 복잡한 스크립트나 파일 교체 없이 Node.js의 표준 기능만으로 구현됩니다.
