import {
  Draw<PERSON>ontextP<PERSON>ider,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@geon-map/react-odf";
import React from "react";

export default function WidgetLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MapProvider projection="EPSG:5186" zoom={10}>
      <LayerProvider>
        <DrawProvider
          continuity={false}
          style={{
            fill: { color: [255, 0, 0, 1] },
            stroke: { color: [0, 255, 0, 0.8], width: 5 },
          }}
          measureOptions={{
            continuity: false,
            style: {
              fill: { color: [0, 100, 0, 0.3] },
              stroke: { color: [0, 100, 0, 1], width: 3 },
            },
          }}
        >
          <DrawContextProvider>{children}</DrawContextProvider>
        </DrawProvider>
      </LayerProvider>
    </MapProvider>
  );
}
