{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "env": ["ANALYZE", "NODE_ENV", "BUILD_MODE"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build:jit": {"dependsOn": ["^build:jit"], "env": ["NODE_ENV", "BUILD_MODE"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "BUILD_MODE"]}, "dev:prebuild": {"cache": false, "persistent": true, "env": ["NODE_ENV", "BUILD_MODE"]}}}