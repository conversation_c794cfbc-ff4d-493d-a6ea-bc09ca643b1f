import NextBundleAnalyzer from "@next/bundle-analyzer";

const isDev = process.env.NODE_ENV === "development";
const isJIT =
  process.env.BUILD_MODE === "jit" || (!process.env.BUILD_MODE && isDev);

/** @type {import('next').NextConfig} */
const nextConfig = {
  turbopack: {
    rules: {
      // svg loader
      "*.svg": {
        loaders: ["@svgr/webpack"],
      },
    },
  },
  webpack(config, options) {
    // svg loader
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    // source map 을 dev 실행시에만 활성화
    if (options.dev) {
      config.devTool = options.isServer
        ? false
        : "eval-cheap-module-source-map";
    }
    return config;
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: "standalone",
  experimental: {
    optimizePackageImports: ["lucide-react"],
  },
  // JIT 모드에서만 transpilePackages 사용 (소스에서 직접 컴파일)
  // 사전 빌드 모드에서는 이미 빌드된 dist 파일 사용
  transpilePackages: isJIT
    ? ["web", "@config/*", "@geon-ui/react", "@geon-query/*", "@geon-map/*"]
    : [],
};

const analyze = process.env.ANALYZE === "true";
const withBundleAnalyze = NextBundleAnalyzer({
  enabled: analyze,
});

export default withBundleAnalyze(nextConfig);
