/**
 * 패키지 exports 설정을 동적으로 생성하는 헬퍼
 * JIT 모드에서는 소스 파일을, 사전 빌드 모드에서는 dist 파일을 참조
 */

const isDev = process.env.NODE_ENV === "development";
const isJIT = process.env.BUILD_MODE === "jit" || (!process.env.BUILD_MODE && isDev);

/**
 * 조건부 exports 설정 생성
 * @param {string} srcPath - 소스 파일 경로
 * @param {string} distPath - 빌드된 파일 경로
 * @returns {object} exports 설정 객체
 */
function createConditionalExports(srcPath = "./src/index.ts", distPath = "./dist/index.js") {
  if (isJIT) {
    // JIT 모드: 소스 파일 직접 참조
    return {
      ".": {
        "types": "./src/index.ts",
        "import": srcPath,
        "default": srcPath
      }
    };
  } else {
    // 사전 빌드 모드: 빌드된 파일 참조
    return {
      ".": {
        "types": "./dist/index.d.ts",
        "import": distPath,
        "require": "./dist/index.cjs",
        "default": distPath
      }
    };
  }
}

module.exports = {
  createConditionalExports,
  isJIT,
  isDev
};
