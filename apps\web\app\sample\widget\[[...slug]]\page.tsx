import { Map } from "@geon-map/react-odf";
import { MouseCoordWidget } from "@geon-map/react-ui/components";

import AddressSearchWidgetPackage from "@/components/widget/address-search-widget-pakage";
import BasemapWidgetPakage from "@/components/widget/basemap-widget-pakage";
import BasemapWidgetUse from "@/components/widget/basemap-widget-use";
import DrawWidgetPakage from "@/components/widget/draw-widget-pakage";
import { MouseCoordDisplay } from "@/components/widget/mouse-coord-display";

export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  console.log("slug", slug);

  return (
    <Map className="h-[800px] w-full">
      {/* <BasemapWidgetUse /> */}
      <BasemapWidgetPakage className="top-20" />
      <BasemapWidgetUse />
      <AddressSearchWidgetPackage></AddressSearchWidgetPackage>
      <DrawWidgetPakage />
      <MouseCoordDisplay />
      <MouseCoordWidget />
    </Map>
  );
}
