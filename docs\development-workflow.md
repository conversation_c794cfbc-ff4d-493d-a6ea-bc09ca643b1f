# 개발 워크플로우 가이드

이 프로젝트는 `@geon-map/core`와 `@geon-map/react-odf` 패키지에 대해 **JIT(Just-In-Time) 컴파일**과 **사전 빌드** 방식을 모두 지원합니다.

## 🔄 두 가지 개발 모드

### 1. JIT 모드 (기본값)
- **특징**: 실시간 컴파일 및 핫 리로드
- **장점**: 소스 코드 변경 시 즉시 반영, 빠른 개발 사이클
- **사용 시기**: 일반적인 개발 작업

### 2. 사전 빌드 모드
- **특징**: 미리 번들링된 파일 사용
- **장점**: 프로덕션과 유사한 환경에서 테스트 가능
- **사용 시기**: 성능 테스트, 번들 크기 확인, 프로덕션 배포 전 검증

## 🚀 사용 방법

### 기본 개발 (JIT 모드)
```bash
# 기본 개발 서버 시작 (JIT 모드)
pnpm dev

# 또는 명시적으로 JIT 모드 시작
pnpm dev:jit
```

### 사전 빌드 모드 개발
```bash
# 사전 빌드 모드로 개발 서버 시작
pnpm dev:prebuild-mode
```

### 모드 전환
```bash
# JIT 모드로 전환
pnpm switch:jit

# 사전 빌드 모드로 전환
pnpm switch:prebuild

# 현재 모드 상태 확인
pnpm status
```

### 빌드
```bash
# 프로덕션 빌드 (최적화된 번들)
pnpm build

# JIT 모드로 빌드 (개발용)
pnpm build:jit

# 사전 빌드 모드로 빌드
pnpm build:prebuild
```

## 🛠️ 워크플로우 관리 도구

편의를 위해 워크플로우 관리 스크립트를 제공합니다:

```bash
# 도움말 보기
pnpm workflow help

# JIT 모드로 개발 시작
pnpm workflow start jit

# 사전 빌드 모드로 개발 시작
pnpm workflow start prebuild

# 모드 전환
pnpm workflow switch jit
pnpm workflow switch prebuild

# 현재 상태 확인
pnpm workflow status
```

## 📁 파일 구조

```
packages/
├── geon-map/
│   ├── core/
│   │   ├── src/           # 소스 파일 (JIT 모드에서 사용)
│   │   ├── dist/          # 빌드된 파일 (사전 빌드 모드에서 사용)
│   │   ├── tsup.config.ts # 빌드 설정 (모드별 최적화)
│   │   └── package.json   # 패키지 설정
│   └── react/
│       └── odf/
│           ├── src/       # 소스 파일
│           ├── dist/      # 빌드된 파일
│           └── ...
├── .env.development       # 개발 환경 설정
├── .env.prebuild         # 사전 빌드 모드 설정
└── scripts/
    └── dev-workflow.js   # 워크플로우 관리 스크립트
```

## ⚙️ 기술적 구현

### 환경 변수
- `NODE_ENV`: 환경 구분 (development/production)
- `BUILD_MODE`: 빌드 모드 (jit/prebuild)

### Next.js 설정
- JIT 모드: `transpilePackages`로 소스에서 직접 컴파일
- 사전 빌드 모드: 빌드된 `dist` 파일 사용

### tsup 설정
- JIT 모드: 최적화 최소화, 빠른 빌드
- 사전 빌드 모드: 완전한 최적화 (minify, treeshake, splitting)

## 🔍 문제 해결

### 모드 전환 후 캐시 문제
```bash
# 캐시 정리
pnpm clean

# 의존성 재설치
pnpm install
```

### 빌드 오류
```bash
# 타입 체크
pnpm check-types

# 린트 검사
pnpm lint
```

## 💡 권장 사항

1. **일반 개발**: JIT 모드 사용 (빠른 피드백)
2. **성능 테스트**: 사전 빌드 모드 사용
3. **배포 전 검증**: 사전 빌드 모드로 최종 테스트
4. **CI/CD**: 프로덕션 빌드 사용

이 워크플로우를 통해 개발 효율성과 프로덕션 안정성을 모두 확보할 수 있습니다.
